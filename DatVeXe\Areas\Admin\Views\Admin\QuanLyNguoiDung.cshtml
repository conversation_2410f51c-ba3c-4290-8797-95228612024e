@model IEnumerable<DatVeXe.Models.NguoiDung>

@{
    ViewData["Title"] = "Quản lý người dùng";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-users" style="color: #3498db;"></i>
        Quản lý người dùng
    </h3>
    <button class="btn" style="background-color: #3498db; border-color: #3498db; color: white;">
        <i class="fas fa-plus"></i>
        Thêm người dùng
    </button>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>ID</th>
                        <th>Họ tên</th>
                        <th>Email</th>
                        <th><PERSON><PERSON> điện thoại</th>
                        <th><PERSON><PERSON><PERSON> đ<PERSON>ng ký</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var nguoiDung in Model)
                    {
                        <tr>
                            <td>@nguoiDung.NguoiDungId</td>
                            <td>@nguoiDung.HoTen</td>
                            <td>@nguoiDung.Email</td>
                            <td>@nguoiDung.SoDienThoai</td>
                            <td>@nguoiDung.NgayDangKy.ToString("dd/MM/yyyy")</td>
                            <td>
                                <span class="badge" style="background-color: #27ae60; color: white;">Hoạt động</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #3498db; color: #3498db;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #f39c12; color: #f39c12;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #e74c3c; color: #e74c3c;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-3">
    <small class="text-muted">
        Tổng cộng: @Model.Count() người dùng
    </small>
</div>
