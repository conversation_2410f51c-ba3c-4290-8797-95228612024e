@model DatVeXe.Models.ChuyenXeSearchViewModel
@{
    ViewData["Title"] = "Danh sách chuyến xe";
}

<div class="container py-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
        <h2 class="fw-bold text-primary mb-0 text-uppercase"><PERSON>h sách chuyến xe</h2>
        <div class="d-flex gap-2">
            <a asp-action="Dashboard" class="btn btn-info d-flex align-items-center gap-2 rounded-pill px-4">
                <i class="fas fa-chart-bar"></i> Thống kê
            </a>
            <a asp-action="Create" class="btn btn-primary d-flex align-items-center gap-2 rounded-pill px-4">
                <i class="bi bi-plus-circle"></i> Thêm chuyến xe mới
            </a>
            @if (Model.KetQua != null && Model.KetQua.Any())
            {
                <button type="button" class="btn btn-success d-flex align-items-center gap-2 rounded-pill px-4" onclick="exportData()">
                    <i class="fas fa-file-excel"></i> Xuất Excel
                </button>
            }
        </div>
    </div>

    @if (TempData["Success"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Success"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["Error"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["Error"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Form tìm kiếm -->
    <div class="card shadow-sm mb-4">
        <div class="card-header search-form">
            <h6 class="card-title mb-0">
                <i class="fas fa-search text-primary"></i> Tìm kiếm chuyến xe
            </h6>
        </div>
        <div class="card-body search-form">
            <form asp-action="Index" method="get" id="searchForm">
                <div class="row g-3">
                    <div class="col-md-2">
                        <label asp-for="DiemDi" class="form-label"></label>
                        <input asp-for="DiemDi" class="form-control" placeholder="Nhập điểm đi" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="DiemDen" class="form-label"></label>
                        <input asp-for="DiemDen" class="form-control" placeholder="Nhập điểm đến" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="TuNgay" class="form-label"></label>
                        <input asp-for="TuNgay" class="form-control" type="date" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="DenNgay" class="form-label"></label>
                        <input asp-for="DenNgay" class="form-control" type="date" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="GiaTu" class="form-label"></label>
                        <input asp-for="GiaTu" class="form-control" placeholder="Giá từ" type="number" min="0" step="1000" />
                    </div>
                    <div class="col-md-2">
                        <label asp-for="GiaDen" class="form-label"></label>
                        <input asp-for="GiaDen" class="form-control" placeholder="Giá đến" type="number" min="0" step="1000" />
                    </div>
                </div>
                <div class="row g-3 mt-2">
                    <div class="col-md-2">
                        <label asp-for="LoaiXe" class="form-label"></label>
                        <select asp-for="LoaiXe" class="form-select">
                            <option value="">Tất cả loại xe</option>
                            <option value="Giường nằm">Giường nằm</option>
                            <option value="Limousine">Limousine</option>
                            <option value="Ghế ngồi">Ghế ngồi</option>
                            <option value="VIP">VIP</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="TrangThai" class="form-label"></label>
                        <select asp-for="TrangThai" class="form-select">
                            <option value="">Tất cả</option>
                            <option value="chua_khoi_hanh">Chưa khởi hành</option>
                            <option value="da_khoi_hanh">Đã khởi hành</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="SortBy" class="form-label">Sắp xếp theo</label>
                        <select asp-for="SortBy" class="form-select">
                            <option value="NgayKhoiHanh">Ngày khởi hành</option>
                            <option value="Gia">Giá vé</option>
                            <option value="DiemDi">Điểm đi</option>
                            <option value="DiemDen">Điểm đến</option>
                            <option value="LoaiXe">Loại xe</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label asp-for="SortOrder" class="form-label">Thứ tự</label>
                        <select asp-for="SortOrder" class="form-select">
                            <option value="asc">Tăng dần</option>
                            <option value="desc">Giảm dần</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <div class="form-check mt-4">
                            <input name="CoGheTrong" class="form-check-input" type="checkbox" value="true" @(Model.CoGheTrong == true ? "checked" : "") />
                            <label for="CoGheTrong" class="form-check-label">Có ghế trống</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Tìm kiếm
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i> Reset
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Hiển thị thống kê kết quả -->
    @if (Model.KetQua != null)
    {
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            Tìm thấy <strong>@Model.KetQua.Count()</strong> chuyến xe
            @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
            {
                <span>phù hợp với điều kiện tìm kiếm</span>
            }
        </div>
    }

    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="border-0">Điểm đi</th>
                            <th class="border-0">Điểm đến</th>
                            <th class="border-0">Ngày khởi hành</th>
                            <th class="border-0">Giá vé</th>
                            <th class="border-0">Xe</th>
                            <th class="border-0">Số ghế trống</th>
                            <th class="border-0">Trạng thái</th>
                            <th class="border-0" style="width: 200px;">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (Model.KetQua == null || !Model.KetQua.Any())
                        {
                            <tr>
                                <td colspan="8" class="text-center py-5 text-muted">
                                    <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                                    @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
                                    {
                                        <span>Không tìm thấy chuyến xe nào phù hợp với điều kiện tìm kiếm.</span>
                                    }
                                    else
                                    {
                                        <span>Chưa có chuyến xe nào.</span>
                                    }
                                </td>
                            </tr>
                        }
                        else
                        {
                            foreach (var item in Model.KetQua)
                            {
                                var soGheTrong = (item.Xe?.SoGhe ?? 0) - (item.Ves?.Count ?? 0);
                                var daDi = item.NgayKhoiHanh <= DateTime.Now;

                                <tr>
                                    <td>@item.DiemDiDisplay</td>
                                    <td>@item.DiemDenDisplay</td>
                                    <td>@item.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        <strong class="text-success">@item.Gia.ToString("N0") VNĐ</strong>
                                    </td>
                                    <td>
                                        @(item.Xe?.BienSo ?? "N/A")
                                        <small class="text-muted d-block">@(item.Xe?.LoaiXe ?? "N/A") - @(item.Xe?.SoGhe ?? 0) chỗ</small>
                                    </td>
                                    <td>
                                        @if (soGheTrong > 0)
                                        {
                                            <span class="badge bg-success">@soGheTrong chỗ trống</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">Hết chỗ</span>
                                        }
                                    </td>
                                    <td>
                                        @if (daDi)
                                        {
                                            <span class="badge bg-secondary">Đã khởi hành</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-primary">Chưa khởi hành</span>
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a asp-action="Details" asp-route-id="@item.ChuyenXeId"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-info-circle"></i>
                                            </a>
                                            @if (!daDi)
                                            {
                                                <a asp-action="Edit" asp-route-id="@item.ChuyenXeId"
                                                   class="btn btn-outline-warning btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                @if (item.Ves == null || !item.Ves.Any())
                                                {
                                                    <a asp-action="Delete" asp-route-id="@item.ChuyenXeId"
                                                       class="btn btn-outline-danger btn-sm">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                }
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        .btn-group .btn {
            margin-right: 5px;
        }
        .btn-group .btn:last-child {
            margin-right: 0;
        }
        .table th {
            font-weight: 600;
            color: #2c3e50;
        }
        .table td {
            color: #2c3e50;
        }
        .badge {
            font-weight: 500;
        }
        .search-form {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        .form-label {
            color: #2c3e50 !important;
            font-weight: 600;
        }
        .form-control, .form-select {
            color: #2c3e50 !important;
        }
        .form-check-label {
            color: #2c3e50 !important;
            font-weight: 500;
        }
        .text-muted {
            color: #6c757d !important;
        }
        .card-title {
            color: #2c3e50 !important;
        }
    </style>
}

@section Scripts {
    <script>
        function exportData() {
            // Lấy các giá trị từ form tìm kiếm
            var form = document.getElementById('searchForm');
            var formData = new FormData(form);
            var params = new URLSearchParams(formData);

            // Tạo URL cho export
            var exportUrl = '@Url.Action("ExportExcel", "ChuyenXe")?' + params.toString();

            // Mở link download
            window.location.href = exportUrl;
        }

        $(document).ready(function() {
            // Auto-submit form khi thay đổi select
            $('#TrangThai').change(function() {
                if ($(this).val() !== '') {
                    $('#searchForm').submit();
                }
            });

            // Validation cho ngày
            $('#TuNgay, #DenNgay').change(function() {
                var tuNgay = $('#TuNgay').val();
                var denNgay = $('#DenNgay').val();

                if (tuNgay && denNgay && new Date(tuNgay) > new Date(denNgay)) {
                    alert('Từ ngày không được lớn hơn đến ngày');
                    $(this).val('');
                }
            });

            // Highlight search results
            @if (Model.DiemDi != null || Model.DiemDen != null || Model.TuNgay != null || Model.DenNgay != null || Model.TrangThai != null || Model.CoGheTrong != null)
            {
                <text>
                $('.alert-info').addClass('border-primary');
                </text>
            }
        });
    </script>
}
