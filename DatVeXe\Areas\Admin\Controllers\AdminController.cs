using Microsoft.AspNetCore.Mvc;
using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;
using DatVeXe.Attributes;

namespace DatVeXe.Areas.Admin.Controllers
{
    [Area("Admin")]
    [AdminAuthorization]
    public class AdminController : Controller
    {
        private readonly DatVeXeContext _context;

        public AdminController(DatVeXeContext context)
        {
            _context = context;
        }

        // Dashboard chính của Admin
        public async Task<IActionResult> Index()
        {
            // Thống kê tổng quan
            var tongNguoiDung = await _context.NguoiDungs.CountAsync();
            var tongBooking = 0; // Removed Ve references
            var tongTuyenDuong = await _context.TuyenDuongs.CountAsync();
            var tongXe = await _context.Xes.CountAsync();

            ViewBag.TongNguoiDung = tongNguoiDung;
            ViewBag.TongBooking = tongBooking;
            ViewBag.TongTuyenDuong = tongTuyenDuong;
            ViewBag.TongXe = tongXe;

            return View();
        }

        // Quản lý người dùng
        public async Task<IActionResult> QuanLyNguoiDung()
        {
            var nguoiDungs = await _context.NguoiDungs.ToListAsync();
            return View(nguoiDungs);
        }

        // Quản lý tuyến đường
        public async Task<IActionResult> QuanLyTuyenDuong()
        {
            var tuyenDuongs = await _context.TuyenDuongs.ToListAsync();
            return View(tuyenDuongs);
        }

        // Quản lý xe
        public async Task<IActionResult> QuanLyXe()
        {
            var xes = await _context.Xes.ToListAsync();
            return View(xes);
        }

        // Quản lý chuyến xe
        public async Task<IActionResult> QuanLyChuyenXe()
        {
            var chuyenXes = await _context.ChuyenXes
                .Include(c => c.Xe)
                .Include(c => c.TuyenDuong)
                .ToListAsync();
            return View(chuyenXes);
        }



        // Báo cáo doanh thu
        public async Task<IActionResult> BaoCaoDoanhThu()
        {
            // Removed Ve references - using empty data for now
            var doanhThuTheoThang = new List<object>();

            return View(doanhThuTheoThang);
        }
    }
}
