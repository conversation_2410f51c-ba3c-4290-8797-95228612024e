@model DatVeXe.Models.PassengerInfoViewModel
@{
    ViewData["Title"] = "Thông tin hành khách";
}

<div class="container py-4">
    <!-- Progress Steps -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="progress-steps">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <div class="step-label">Ch<PERSON><PERSON> ch<PERSON>ến</div>
                </div>
                <div class="step completed">
                    <div class="step-number">2</div>
                    <div class="step-label">Chọn ghế</div>
                </div>
                <div class="step active">
                    <div class="step-number">3</div>
                    <div class="step-label">Thông tin</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-label">Thanh toán</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Booking Summary -->
        <div class="col-lg-4 mb-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clipboard-check me-2"></i>Tóm tắt đặt vé
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Trip Info -->
                    <div class="booking-summary">
                        <div class="summary-section mb-3">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="bi bi-geo-alt me-1"></i>Tuyến đường
                            </h6>
                            <div class="route-display">
                                <div class="route-point">
                                    <i class="bi bi-circle-fill text-success"></i>
                                    <span>@Model.ChuyenXe.DiemDiDisplay</span>
                                </div>
                                <div class="route-line"></div>
                                <div class="route-point">
                                    <i class="bi bi-geo-alt-fill text-danger"></i>
                                    <span>@Model.ChuyenXe.DiemDenDisplay</span>
                                </div>
                            </div>
                        </div>

                        <div class="summary-section mb-3">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="bi bi-calendar-event me-1"></i>Thời gian
                            </h6>
                            <div class="time-info">
                                <div>@Model.ChuyenXe.NgayKhoiHanh.ToString("dddd, dd/MM/yyyy")</div>
                                <div class="fw-bold text-success">@Model.ChuyenXe.NgayKhoiHanh.ToString("HH:mm")</div>
                            </div>
                        </div>

                        <div class="summary-section mb-3">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="bi bi-truck me-1"></i>Phương tiện
                            </h6>
                            <div>@Model.ChuyenXe.Xe?.LoaiXe</div>
                            <small class="text-muted">@Model.ChuyenXe.Xe?.BienSo</small>
                        </div>

                        <div class="summary-section mb-3">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="bi bi-square-fill me-1"></i>Ghế ngồi
                            </h6>
                            <div class="seat-display">
                                <span class="badge bg-success fs-6">Ghế @Model.ChoNgoi.SoGhe</span>
                                <small class="d-block text-muted">@Model.ChoNgoi.LoaiGhe</small>
                            </div>
                        </div>

                        <hr>

                        <div class="summary-section">
                            <h6 class="fw-bold text-primary mb-2">
                                <i class="bi bi-currency-dollar me-1"></i>Tổng tiền
                            </h6>
                            <div class="price-display">
                                <span class="fs-4 fw-bold text-success">@string.Format("{0:N0}", Model.ChuyenXe.Gia) VNĐ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Passenger Information Form -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-person-fill me-2"></i>Thông tin hành khách
                    </h5>
                </div>
                <div class="card-body p-4">
                    <!-- Danh bạ hành khách -->
                    @if (ViewBag.DanhBaHanhKhach != null && ((List<DanhBaHanhKhach>)ViewBag.DanhBaHanhKhach).Any())
                    {
                        <div class="card bg-light border-0 mb-4">
                            <div class="card-body">
                                <h6 class="fw-bold mb-3">
                                    <i class="bi bi-person-lines-fill text-primary me-2"></i>Chọn từ danh bạ
                                </h6>
                                <div class="row g-2">
                                    @foreach (var contact in (List<DanhBaHanhKhach>)ViewBag.DanhBaHanhKhach)
                                    {
                                        <div class="col-md-6 col-lg-4">
                                            <div class="contact-card" onclick="selectContact(@contact.DanhBaId, '@contact.TenHanhKhach', '@contact.SoDienThoai', '@contact.Email', '@contact.GioiTinh', '@contact.NgaySinh?.ToString("yyyy-MM-dd")', '@contact.CCCD')">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white me-2">
                                                        @contact.TenHanhKhach.Substring(0, 1).ToUpper()
                                                    </div>
                                                    <div class="flex-grow-1">
                                                        <div class="fw-semibold">@contact.TenHanhKhach</div>
                                                        <small class="text-muted">@contact.SoDienThoai</small>
                                                        @if (contact.SoLanSuDung > 0)
                                                        {
                                                            <small class="text-success d-block">
                                                                <i class="bi bi-check-circle me-1"></i>Đã dùng @contact.SoLanSuDung lần
                                                            </small>
                                                        }
                                                    </div>
                                                    <i class="bi bi-chevron-right text-muted"></i>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                                <div class="text-center mt-3">
                                    <a asp-controller="Contacts" asp-action="Index" class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="bi bi-plus-circle me-2"></i>Quản lý danh bạ
                                    </a>
                                </div>
                            </div>
                        </div>
                    }

                    <form asp-action="PassengerInfo" asp-route-sessionId="@ViewBag.SessionId" method="post" id="passengerForm">
                        <input type="hidden" asp-for="ChuyenXeId" />
                        <input type="hidden" asp-for="ChoNgoiId" />

                        <div class="row g-3">
                            <!-- Tên khách hàng -->
                            <div class="col-md-6">
                                <label asp-for="TenKhach" class="form-label fw-semibold required">
                                    <i class="bi bi-person text-primary me-1"></i>Họ và tên
                                </label>
                                <input asp-for="TenKhach" class="form-control form-control-lg" 
                                       placeholder="Nhập họ và tên đầy đủ" />
                                <span asp-validation-for="TenKhach" class="text-danger"></span>
                            </div>

                            <!-- Số điện thoại -->
                            <div class="col-md-6">
                                <label asp-for="SoDienThoai" class="form-label fw-semibold required">
                                    <i class="bi bi-telephone text-success me-1"></i>Số điện thoại
                                </label>
                                <input asp-for="SoDienThoai" class="form-control form-control-lg" 
                                       placeholder="Nhập số điện thoại" />
                                <span asp-validation-for="SoDienThoai" class="text-danger"></span>
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <label asp-for="Email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope text-warning me-1"></i>Email
                                </label>
                                <input asp-for="Email" type="email" class="form-control form-control-lg" 
                                       placeholder="Nhập địa chỉ email (tùy chọn)" />
                                <span asp-validation-for="Email" class="text-danger"></span>
                                <div class="form-text">
                                    <i class="bi bi-info-circle me-1"></i>Email để nhận thông tin xác nhận vé
                                </div>
                            </div>

                            <!-- Ghi chú -->
                            <div class="col-md-6">
                                <label asp-for="GhiChu" class="form-label fw-semibold">
                                    <i class="bi bi-chat-text text-info me-1"></i>Ghi chú
                                </label>
                                <textarea asp-for="GhiChu" class="form-control" rows="3" 
                                          placeholder="Ghi chú thêm (tùy chọn)"></textarea>
                                <span asp-validation-for="GhiChu" class="text-danger"></span>
                            </div>

                            <!-- Notification preferences -->
                            <div class="col-12">
                                <div class="card bg-light border-0">
                                    <div class="card-body">
                                        <h6 class="fw-bold mb-3">
                                            <i class="bi bi-bell text-primary me-1"></i>Tùy chọn nhận thông báo
                                        </h6>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input asp-for="NhanSMS" class="form-check-input" type="checkbox" />
                                                    <label asp-for="NhanSMS" class="form-check-label">
                                                        <i class="bi bi-phone me-1"></i>Nhận thông báo qua SMS
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input asp-for="NhanEmail" class="form-check-input" type="checkbox" />
                                                    <label asp-for="NhanEmail" class="form-check-label">
                                                        <i class="bi bi-envelope me-1"></i>Nhận thông báo qua Email
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <small class="text-muted">
                                            <i class="bi bi-info-circle me-1"></i>
                                            Chúng tôi sẽ gửi thông tin xác nhận vé và cập nhật trạng thái chuyến xe
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg px-4">
                                        <i class="bi bi-arrow-left me-2"></i>Quay lại
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-lg px-5" id="continueBtn">
                                        <i class="bi bi-arrow-right me-2"></i>Tiếp tục thanh toán
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        $(document).ready(function() {
            // Form validation
            $('#passengerForm').on('submit', function(e) {
                if (!$(this).valid()) {
                    e.preventDefault();
                    // Scroll to first error
                    $('html, body').animate({
                        scrollTop: $('.field-validation-error:first').offset().top - 100
                    }, 500);
                    return false;
                }
                
                // Show loading state
                $('#continueBtn').prop('disabled', true)
                    .html('<i class="spinner-border spinner-border-sm me-2"></i>Đang xử lý...');
            });

            // Auto-enable email notification if email is provided
            $('#Email').on('input', function() {
                if ($(this).val().trim()) {
                    $('#NhanEmail').prop('checked', true);
                }
            });

            // Phone number formatting
            $('#SoDienThoai').on('input', function() {
                let value = $(this).val().replace(/\D/g, '');
                if (value.length > 10) {
                    value = value.substring(0, 10);
                }
                $(this).val(value);
            });

            // Name formatting
            $('#TenKhach').on('input', function() {
                let value = $(this).val();
                // Capitalize first letter of each word
                value = value.toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
                $(this).val(value);
            });
        });

        // Function to select contact from address book
        function selectContact(contactId, name, phone, email, gender, birthDate, cccd) {
            // Fill form with contact data
            $('#TenKhach').val(name);
            $('#SoDienThoai').val(phone);
            $('#Email').val(email || '');

            // Auto-enable notifications if email provided
            if (email) {
                $('#NhanEmail').prop('checked', true);
            }

            // Update usage count via AJAX
            $.post('@Url.Action("UpdateUsage", "Contacts")', { id: contactId }, function(result) {
                if (result.success) {
                    // Show success message
                    toastr.success('Đã chọn thông tin từ danh bạ: ' + name);

                    // Highlight selected contact
                    $('.contact-card').removeClass('selected');
                    $('[onclick*="' + contactId + '"]').addClass('selected');
                }
            });

            // Scroll to form
            $('html, body').animate({
                scrollTop: $('#passengerForm').offset().top - 100
            }, 500);
        }
    </script>
}

<style>
    .progress-steps {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 2rem;
    }

    .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 0 2rem;
        position: relative;
    }

    .step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 100%;
        width: 4rem;
        height: 2px;
        background: #dee2e6;
        z-index: -1;
    }

    .step.completed::after,
    .step.active::after {
        background: #28a745;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #dee2e6;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .step.completed .step-number {
        background: #28a745;
        color: white;
    }

    .step.active .step-number {
        background: #007bff;
        color: white;
    }

    .step-label {
        font-size: 0.875rem;
        color: #6c757d;
        font-weight: 500;
    }

    .step.completed .step-label,
    .step.active .step-label {
        color: #495057;
        font-weight: 600;
    }

    .booking-summary .summary-section {
        padding: 0.75rem 0;
    }

    .route-display {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .route-point {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
    }

    .route-line {
        width: 2px;
        height: 20px;
        background: #dee2e6;
        margin-left: 6px;
    }

    .time-info {
        line-height: 1.4;
    }

    .seat-display {
        text-align: left;
    }

    .price-display {
        text-align: left;
    }

    .form-control-lg {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-control-lg:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .required::after {
        content: " *";
        color: #dc3545;
    }

    .btn-lg {
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-lg:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .form-check-input:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
    }

    .contact-card {
        padding: 0.75rem;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
    }

    .contact-card:hover {
        border-color: #0d6efd;
        background: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .contact-card.selected {
        border-color: #28a745;
        background: #d4edda;
    }

    .avatar-sm {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.875rem;
    }
</style>
