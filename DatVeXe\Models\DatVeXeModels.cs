#pragma warning disable CS8618
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace DatVeXe.Models
{
    // Enum cho trạng thái vé
    public enum TrangThaiVe
    {
        [Display(Name = "Đã đặt")]
        DaDat = 1,
        [Display(Name = "Đã thanh toán")]
        DaThanhToan = 2,
        [Display(Name = "Đã hoàn thành")]
        DaHoanThanh = 3,
        [Display(Name = "Đã hủy")]
        DaHuy = 4,
        [Display(Name = "Đã sử dụng")]
        DaSuDung = 5,
        [Display(Name = "Đã hoàn tiền")]
        DaHoanTien = 6
    }

    // Enum cho trạng thái chuyến xe
    public enum TrangThaiChuyenXe
    {
        [Display(Name = "Hoạt động")]
        HoatDong = 1,
        [Display(Name = "Tạm dừng")]
        TamDung = 2,
        [Display(Name = "Đã hủy")]
        DaHuy = 3,
        [Display(Name = "Hoàn thành")]
        HoanThanh = 4
    }

    // Model cho tuyến đường
    public class TuyenDuong
    {
        [Key]
        public int TuyenDuongId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên tuyến đường")]
        [StringLength(200)]
        [Display(Name = "Tên tuyến đường")]
        public string TenTuyen { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập điểm đi")]
        [StringLength(100)]
        [Display(Name = "Điểm đi")]
        public string DiemDi { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập điểm đến")]
        [StringLength(100)]
        [Display(Name = "Điểm đến")]
        public string DiemDen { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập khoảng cách")]
        [Display(Name = "Khoảng cách (km)")]
        [Range(1, 10000, ErrorMessage = "Khoảng cách phải từ 1 đến 10000 km")]
        public int KhoangCach { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập thời gian dự kiến")]
        [Display(Name = "Thời gian dự kiến")]
        [DataType(DataType.Time)]
        public TimeSpan ThoiGianDuKien { get; set; }

        [StringLength(500)]
        [Display(Name = "Mô tả")]
        public string? MoTa { get; set; }

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; } = true;

        [Display(Name = "Ngày tạo")]
        [DataType(DataType.DateTime)]
        public DateTime NgayTao { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<ChuyenXe>? ChuyenXes { get; set; }
    }
    public class Xe
    {
        [Key]
        public int XeId { get; set; }
        [Required(ErrorMessage = "Vui lòng nhập biển số xe")]
        [StringLength(50)]
        [Display(Name = "Biển số xe")]
        public string BienSo { get; set; } = string.Empty;
        [Required(ErrorMessage = "Vui lòng nhập loại xe")]
        [StringLength(100)]
        [Display(Name = "Loại xe")]
        public string LoaiXe { get; set; } = string.Empty;
        [Required(ErrorMessage = "Vui lòng nhập số ghế")]
        [Display(Name = "Số ghế")]
        [Range(1, 100, ErrorMessage = "Số ghế phải từ 1 đến 100")]
        public int SoGhe { get; set; }

        [StringLength(100)]
        [Display(Name = "Nhà xe")]
        public string? NhaXe { get; set; }

        [StringLength(500)]
        [Display(Name = "Mô tả")]
        public string? MoTa { get; set; }

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; } = true;

        [Display(Name = "Ngày tạo")]
        public DateTime NgayTao { get; set; } = DateTime.Now;

        // Navigation properties
        public virtual ICollection<ChuyenXe>? ChuyenXes { get; set; }
        public virtual ICollection<ChoNgoi>? ChoNgois { get; set; }
    }

    // Model cho chỗ ngồi
    public class ChoNgoi
    {
        [Key]
        public int ChoNgoiId { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn xe")]
        [Display(Name = "Xe")]
        public int XeId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập số ghế")]
        [StringLength(10)]
        [Display(Name = "Số ghế")]
        public string SoGhe { get; set; } = string.Empty;

        [Display(Name = "Vị trí hàng")]
        [Range(1, 20, ErrorMessage = "Vị trí hàng phải từ 1 đến 20")]
        public int Hang { get; set; }

        [Display(Name = "Vị trí cột")]
        [Range(1, 10, ErrorMessage = "Vị trí cột phải từ 1 đến 10")]
        public int Cot { get; set; }

        [Display(Name = "Loại ghế")]
        [StringLength(50)]
        public string LoaiGhe { get; set; } = "Thường"; // Thường, VIP, Giường nằm

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; } = true;

        // Navigation properties
        [ForeignKey("XeId")]
        public virtual Xe? Xe { get; set; }

        public virtual ICollection<Ve>? Ves { get; set; }
    }

    public class ChuyenXe
    {
        [Key]
        public int ChuyenXeId { get; set; }

        // Thay đổi để sử dụng TuyenDuong
        [Display(Name = "Tuyến đường")]
        public int? TuyenDuongId { get; set; }

        // Giữ lại để tương thích với dữ liệu cũ
        [StringLength(100)]
        [Display(Name = "Điểm đi")]
        public string? DiemDi { get; set; }

        [StringLength(100)]
        [Display(Name = "Điểm đến")]
        public string? DiemDen { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn ngày khởi hành")]
        [Display(Name = "Ngày khởi hành")]
        [DataType(DataType.DateTime)]
        public DateTime NgayKhoiHanh { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn xe")]
        [Display(Name = "Xe")]
        public int XeId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập giá vé")]
        [Display(Name = "Giá vé")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá vé phải lớn hơn 0")]
        public decimal Gia { get; set; }

        [Display(Name = "Thời gian đi")]
        [DataType(DataType.Time)]
        public TimeSpan ThoiGianDi { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }

        [Display(Name = "Trạng thái")]
        public bool TrangThai { get; set; } = true; // true: hoạt động, false: hủy

        [Display(Name = "Ngày tạo")]
        [DataType(DataType.DateTime)]
        public DateTime NgayTao { get; set; } = DateTime.Now;

        [Display(Name = "Trạng thái chuyến xe")]
        public TrangThaiChuyenXe TrangThaiChuyenXe { get; set; } = TrangThaiChuyenXe.HoatDong;

        // Navigation properties
        [ForeignKey("TuyenDuongId")]
        public virtual TuyenDuong? TuyenDuong { get; set; }

        [ForeignKey("XeId")]
        public virtual Xe? Xe { get; set; }

        public virtual ICollection<Ve>? Ves { get; set; }

        // Computed properties để tương thích
        [NotMapped]
        public string DiemDiDisplay => TuyenDuong?.DiemDi ?? DiemDi ?? "";

        [NotMapped]
        public string DiemDenDisplay => TuyenDuong?.DiemDen ?? DiemDen ?? "";
    }

    public class Ve
    {
        [Key]
        public int VeId { get; set; }

        [Required(ErrorMessage = "Vui lòng chọn chuyến xe")]
        [Display(Name = "Chuyến xe")]
        public int ChuyenXeId { get; set; }

        // Người dùng đặt vé
        [Display(Name = "Người dùng")]
        public int? NguoiDungId { get; set; }

        // Chỗ ngồi cụ thể
        [Display(Name = "Chỗ ngồi")]
        public int? ChoNgoiId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ (phải bắt đầu bằng số 0 và có 10 số)")]
        [Display(Name = "Số điện thoại")]
        public string SoDienThoai { get; set; } = string.Empty;

        [StringLength(200)]
        [Display(Name = "Email")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        public string? Email { get; set; }

        [Display(Name = "Ngày đặt")]
        [DataType(DataType.DateTime)]
        public DateTime NgayDat { get; set; } = DateTime.Now;

        [Display(Name = "Trạng thái vé")]
        public TrangThaiVe TrangThai { get; set; } = TrangThaiVe.DaDat;

        [Display(Name = "Giá vé")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá vé phải lớn hơn 0")]
        public decimal GiaVe { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }

        [Display(Name = "Ngày hủy")]
        [DataType(DataType.DateTime)]
        public DateTime? NgayHuy { get; set; }

        [StringLength(500)]
        [Display(Name = "Lý do hủy")]
        public string? LyDoHuy { get; set; }

        [Display(Name = "Mã vé")]
        [StringLength(50)]
        public string MaVe { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("ChuyenXeId")]
        public virtual ChuyenXe? ChuyenXe { get; set; }

        [ForeignKey("NguoiDungId")]
        public virtual NguoiDung? NguoiDung { get; set; }

        [ForeignKey("ChoNgoiId")]
        public virtual ChoNgoi? ChoNgoi { get; set; }

        public virtual ICollection<ThanhToan>? ThanhToans { get; set; }
    }

    public class NguoiDung
    {
        [Key]
        public int NguoiDungId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập họ tên")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Họ tên phải từ 2 đến 100 ký tự")]
        [Display(Name = "Họ tên")]
        public string HoTen { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập email")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập mật khẩu")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Mật khẩu phải từ 6 ký tự trở lên")]
        [Display(Name = "Mật khẩu")]
        public string MatKhau { get; set; } = string.Empty;

        [StringLength(15)]
        [Display(Name = "Số điện thoại")]
        [RegularExpression(@"^(0[0-9]{9})$", ErrorMessage = "Số điện thoại không hợp lệ")]
        public string? SoDienThoai { get; set; }

        [StringLength(200)]
        [Display(Name = "Địa chỉ")]
        public string? DiaChi { get; set; }

        [Display(Name = "Ngày sinh")]
        [DataType(DataType.Date)]
        public DateTime? NgaySinh { get; set; }

        [Display(Name = "Giới tính")]
        [StringLength(10)]
        public string? GioiTinh { get; set; }

        [Display(Name = "Là admin")]
        public bool LaAdmin { get; set; }

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; } = true;

        [Display(Name = "Ngày đăng ký")]
        [DataType(DataType.DateTime)]
        public DateTime NgayDangKy { get; set; } = DateTime.Now;

        [Display(Name = "Lần đăng nhập cuối")]
        [DataType(DataType.DateTime)]
        public DateTime? LanDangNhapCuoi { get; set; }

        // Navigation property
        public virtual ICollection<Ve>? Ves { get; set; }
    }

    // ViewModel cho tìm kiếm chuyến xe
    public class ChuyenXeSearchViewModel
    {
        [Display(Name = "Điểm đi")]
        public string? DiemDi { get; set; }

        [Display(Name = "Điểm đến")]
        public string? DiemDen { get; set; }

        [Display(Name = "Từ ngày")]
        [DataType(DataType.Date)]
        public DateTime? TuNgay { get; set; }

        [Display(Name = "Đến ngày")]
        [DataType(DataType.Date)]
        public DateTime? DenNgay { get; set; }

        [Display(Name = "Trạng thái")]
        public string? TrangThai { get; set; } // "all", "chua_khoi_hanh", "da_khoi_hanh"

        [Display(Name = "Có ghế trống")]
        public bool? CoGheTrong { get; set; }

        [Display(Name = "Giá từ")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal? GiaTu { get; set; }

        [Display(Name = "Giá đến")]
        [Range(0, double.MaxValue, ErrorMessage = "Giá phải lớn hơn 0")]
        public decimal? GiaDen { get; set; }

        [Display(Name = "Loại xe")]
        public string? LoaiXe { get; set; }

        [Display(Name = "Nhà xe")]
        public string? NhaXe { get; set; }

        [Display(Name = "Giờ khởi hành từ")]
        [DataType(DataType.Time)]
        public TimeSpan? GioKhoiHanhTu { get; set; }

        [Display(Name = "Giờ khởi hành đến")]
        [DataType(DataType.Time)]
        public TimeSpan? GioKhoiHanhDen { get; set; }

        [Display(Name = "Sắp xếp theo")]
        public string? SortBy { get; set; } = "NgayKhoiHanh";

        [Display(Name = "Thứ tự")]
        public string? SortOrder { get; set; } = "asc";

        // Kết quả tìm kiếm
        public IEnumerable<ChuyenXe>? KetQua { get; set; }
    }

    // ViewModel cho thống kê
    public class ThongKeViewModel
    {
        public int TongChuyenXe { get; set; }
        public int ChuyenXeHomNay { get; set; }
        public int ChuyenXeThangNay { get; set; }
        public int TongVeDaBan { get; set; }
        public int ChuaKhoiHanh { get; set; }
        public int DaKhoiHanh { get; set; }
        public decimal DoanhThuThangNay { get; set; }
        public decimal DoanhThuHomNay { get; set; }
        public List<dynamic>? TopTuyen { get; set; }
        public List<dynamic>? ThongKeXe { get; set; }
        public List<dynamic>? DoanhThuTheoNgay { get; set; }
    }





    // ViewModel cho quản lý tuyến đường
    public class TuyenDuongViewModel
    {
        [Display(Name = "Điểm đi")]
        public string DiemDi { get; set; } = "";

        [Display(Name = "Điểm đến")]
        public string DiemDen { get; set; } = "";

        [Display(Name = "Số chuyến xe")]
        public int SoChuyenXe { get; set; }

        [Display(Name = "Tổng vé đã bán")]
        public int TongVeDaBan { get; set; }

        [Display(Name = "Doanh thu")]
        public decimal DoanhThu { get; set; }

        [Display(Name = "Giá thấp nhất")]
        public decimal GiaThapNhat { get; set; }

        [Display(Name = "Giá cao nhất")]
        public decimal GiaCaoNhat { get; set; }

        [Display(Name = "Chuyến xe gần nhất")]
        public DateTime ChuyenXeGanNhat { get; set; }

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; }

        public string TenTuyenDuong => $"{DiemDi} - {DiemDen}";
    }

    // ViewModel cho chi tiết tuyến đường
    public class TuyenDuongDetailViewModel
    {
        public string DiemDi { get; set; } = "";
        public string DiemDen { get; set; } = "";
        public List<ChuyenXe> ChuyenXes { get; set; } = new List<ChuyenXe>();
        public int SoChuyenXe { get; set; }
        public int TongVeDaBan { get; set; }
        public decimal DoanhThu { get; set; }
        public decimal GiaThapNhat { get; set; }
        public decimal GiaCaoNhat { get; set; }
        public int ChuyenXeSapToi { get; set; }
        public int ChuyenXeDaHoanThanh { get; set; }
        public string TenTuyenDuong => $"{DiemDi} - {DiemDen}";
    }



    // ViewModel cho thống kê doanh thu chi tiết
    public class ThongKeDoanhThuViewModel
    {
        [Display(Name = "Từ ngày")]
        [DataType(DataType.Date)]
        public DateTime TuNgay { get; set; } = DateTime.Now.AddDays(-30);

        [Display(Name = "Đến ngày")]
        [DataType(DataType.Date)]
        public DateTime DenNgay { get; set; } = DateTime.Now;

        [Display(Name = "Loại thống kê")]
        public string LoaiThongKe { get; set; } = "ngay"; // ngay, tuan, thang

        public decimal TongDoanhThu { get; set; }
        public decimal DoanhThuTrungBinh { get; set; }
        public int TongVeDaBan { get; set; }
        public decimal GiaVeTrungBinh { get; set; }

        public List<DoanhThuTheoNgayViewModel> DoanhThuTheoNgay { get; set; } = new List<DoanhThuTheoNgayViewModel>();
        public List<DoanhThuTheoTuyenViewModel> DoanhThuTheoTuyen { get; set; } = new List<DoanhThuTheoTuyenViewModel>();
        public List<DoanhThuTheoXeViewModel> DoanhThuTheoXe { get; set; } = new List<DoanhThuTheoXeViewModel>();
    }

    public class DoanhThuTheoNgayViewModel
    {
        public DateTime Ngay { get; set; }
        public decimal DoanhThu { get; set; }
        public int SoVe { get; set; }
        public int SoChuyenXe { get; set; }
    }

    public class DoanhThuTheoTuyenViewModel
    {
        public string TuyenDuong { get; set; } = "";
        public decimal DoanhThu { get; set; }
        public int SoVe { get; set; }
        public int SoChuyenXe { get; set; }
        public decimal GiaVeTrungBinh { get; set; }
    }

    public class DoanhThuTheoXeViewModel
    {
        public string BienSo { get; set; } = "";
        public string LoaiXe { get; set; } = "";
        public decimal DoanhThu { get; set; }
        public int SoVe { get; set; }
        public int SoChuyenXe { get; set; }
        public decimal TyLeLapDay { get; set; }
    }

    // ViewModels cho chọn chỗ ngồi
    public class ChonChoNgoiViewModel
    {
        public ChuyenXe ChuyenXe { get; set; } = new ChuyenXe();
        public List<ChoNgoiViewModel> DanhSachChoNgoi { get; set; } = new List<ChoNgoiViewModel>();
        public int? ChoNgoiDaChon { get; set; }
        public string TenKhach { get; set; } = string.Empty;
        public string SoDienThoai { get; set; } = string.Empty;
        public string? Email { get; set; }
        public string? GhiChu { get; set; }
    }

    // Enums cho thanh toán
    public enum PhuongThucThanhToan
    {
        [Display(Name = "Thanh toán tại quầy")]
        TaiQuay = 1,
        [Display(Name = "VNPay")]
        VNPay = 2,
        [Display(Name = "MoMo")]
        MoMo = 3,
        [Display(Name = "ZaloPay")]
        ZaloPay = 4,
        [Display(Name = "Chuyển khoản ngân hàng")]
        ChuyenKhoan = 5
    }

    public enum TrangThaiThanhToan
    {
        [Display(Name = "Chờ thanh toán")]
        ChoThanhToan = 1,
        [Display(Name = "Đang xử lý")]
        DangXuLy = 2,
        [Display(Name = "Thành công")]
        ThanhCong = 3,
        [Display(Name = "Thất bại")]
        ThatBai = 4,
        [Display(Name = "Đã hủy")]
        DaHuy = 5,
        [Display(Name = "Hoàn tiền")]
        HoanTien = 6
    }

    public class ChoNgoiViewModel
    {
        public int ChoNgoiId { get; set; }
        public string SoGhe { get; set; } = string.Empty;
        public int Hang { get; set; }
        public int Cot { get; set; }
        public string LoaiGhe { get; set; } = string.Empty;
        public bool TrangThaiHoatDong { get; set; }
        public bool DaDat { get; set; }
        public bool DangGiu { get; set; }
        public string? TenKhachDat { get; set; }
        public DateTime? ThoiGianGiu { get; set; }
    }

    public class SoDoGheViewModel
    {
        public int XeId { get; set; }
        public string BienSo { get; set; } = string.Empty;
        public string LoaiXe { get; set; } = string.Empty;
        public int SoGhe { get; set; }
        public int SoHang { get; set; }
        public int SoCot { get; set; }
        public List<ChoNgoiViewModel> DanhSachGhe { get; set; } = new List<ChoNgoiViewModel>();
    }

    public class DatVeVoiChoNgoiViewModel
    {
        public int ChuyenXeId { get; set; }
        public int? ChoNgoiId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên khách hàng")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "Tên khách hàng phải từ 2 đến 100 ký tự")]
        [Display(Name = "Tên khách hàng")]
        public string TenKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [StringLength(15, MinimumLength = 10, ErrorMessage = "Số điện thoại phải từ 10 đến 15 ký tự")]
        [Display(Name = "Số điện thoại")]
        [RegularExpression(@"^[0-9+\-\s\(\)]+$", ErrorMessage = "Số điện thoại không hợp lệ")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }
    }

    // Model cho việc giữ chỗ tạm thời
    public class SeatReservation
    {
        [Key]
        public int ReservationId { get; set; }

        [Required]
        public int ChuyenXeId { get; set; }

        [Required]
        public int ChoNgoiId { get; set; }

        [StringLength(50)]
        public string? SessionId { get; set; }

        [StringLength(100)]
        public string? UserEmail { get; set; }

        [Required]
        public DateTime ReservedAt { get; set; } = DateTime.Now;

        [Required]
        public DateTime ExpiresAt { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation properties
        [ForeignKey("ChuyenXeId")]
        public virtual ChuyenXe? ChuyenXe { get; set; }

        [ForeignKey("ChoNgoiId")]
        public virtual ChoNgoi? ChoNgoi { get; set; }
    }

    // Model cho thanh toán
    public class ThanhToan
    {
        [Key]
        public int ThanhToanId { get; set; }

        [Required]
        public int VeId { get; set; }

        [Required]
        [Display(Name = "Mã giao dịch")]
        [StringLength(100)]
        public string MaGiaoDich { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Phương thức thanh toán")]
        public PhuongThucThanhToan PhuongThucThanhToan { get; set; }

        [Required]
        [Display(Name = "Số tiền")]
        [Range(0, double.MaxValue)]
        public decimal SoTien { get; set; }

        [Display(Name = "Trạng thái")]
        public TrangThaiThanhToan TrangThai { get; set; } = TrangThaiThanhToan.ChoThanhToan;

        [Display(Name = "Thời gian tạo")]
        public DateTime ThoiGianTao { get; set; } = DateTime.Now;

        [Display(Name = "Thời gian thanh toán")]
        public DateTime? ThoiGianThanhToan { get; set; }

        [Display(Name = "Ngày thanh toán")]
        public DateTime NgayThanhToan { get; set; } = DateTime.Now;

        [Display(Name = "Mã phản hồi từ gateway")]
        [StringLength(500)]
        public string? MaPhanHoi { get; set; }

        [Display(Name = "Thông tin phản hồi")]
        [StringLength(1000)]
        public string? ThongTinPhanHoi { get; set; }

        [StringLength(500)]
        [Display(Name = "Ghi chú")]
        public string? GhiChu { get; set; }

        // Navigation properties
        [ForeignKey("VeId")]
        public virtual Ve? Ve { get; set; }
    }

    // Model cho danh bạ hành khách
    public class DanhBaHanhKhach
    {
        [Key]
        public int DanhBaId { get; set; }

        [Required]
        public int NguoiDungId { get; set; }

        [Required(ErrorMessage = "Vui lòng nhập tên")]
        [StringLength(100, MinimumLength = 2)]
        [Display(Name = "Tên hành khách")]
        public string TenHanhKhach { get; set; } = string.Empty;

        [Required(ErrorMessage = "Vui lòng nhập số điện thoại")]
        [StringLength(15, MinimumLength = 10)]
        [Display(Name = "Số điện thoại")]
        [RegularExpression(@"^[0-9+\-\s\(\)]+$", ErrorMessage = "Số điện thoại không hợp lệ")]
        public string SoDienThoai { get; set; } = string.Empty;

        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string? Email { get; set; }

        [StringLength(200)]
        [Display(Name = "Địa chỉ")]
        public string? DiaChi { get; set; }

        [Display(Name = "Ngày sinh")]
        [DataType(DataType.Date)]
        public DateTime? NgaySinh { get; set; }

        [StringLength(10)]
        [Display(Name = "Giới tính")]
        public string? GioiTinh { get; set; } // Nam, Nữ, Khác

        [StringLength(20)]
        [Display(Name = "CCCD/CMND")]
        public string? CCCD { get; set; }

        [Display(Name = "Ngày tạo")]
        public DateTime NgayTao { get; set; } = DateTime.Now;

        [Display(Name = "Lần sử dụng cuối")]
        public DateTime? LanSuDungCuoi { get; set; }

        [Display(Name = "Số lần sử dụng")]
        public int SoLanSuDung { get; set; } = 0;

        [Display(Name = "Ghi chú")]
        [StringLength(500)]
        public string? GhiChu { get; set; }

        // Navigation properties
        [ForeignKey("NguoiDungId")]
        public virtual NguoiDung? NguoiDung { get; set; }
    }

    // Model cho đánh giá chuyến đi
    public class DanhGiaChuyenDi
    {
        [Key]
        public int DanhGiaId { get; set; }

        [Required]
        public int VeId { get; set; }

        [Required]
        public int NguoiDungId { get; set; }

        [Required]
        [Range(1, 5, ErrorMessage = "Điểm đánh giá từ 1 đến 5")]
        [Display(Name = "Điểm đánh giá")]
        public int DiemDanhGia { get; set; }

        [StringLength(1000)]
        [Display(Name = "Nội dung đánh giá")]
        public string? NoiDungDanhGia { get; set; }

        [Display(Name = "Thời gian đánh giá")]
        public DateTime ThoiGianDanhGia { get; set; } = DateTime.Now;

        [Display(Name = "Đánh giá tài xế")]
        [Range(1, 5)]
        public int? DanhGiaTaiXe { get; set; }

        [Display(Name = "Đánh giá xe")]
        [Range(1, 5)]
        public int? DanhGiaXe { get; set; }

        [Display(Name = "Đánh giá dịch vụ")]
        [Range(1, 5)]
        public int? DanhGiaDichVu { get; set; }

        [Display(Name = "Có khuyến nghị")]
        public bool CoKhuyenNghi { get; set; } = true;

        // Navigation properties
        [ForeignKey("VeId")]
        public virtual Ve? Ve { get; set; }

        [ForeignKey("NguoiDungId")]
        public virtual NguoiDung? NguoiDung { get; set; }
    }

    // Model cho khuyến mãi
    public class KhuyenMai
    {
        [Key]
        public int KhuyenMaiId { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "Tên khuyến mãi")]
        public string TenKhuyenMai { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Display(Name = "Mã khuyến mãi")]
        public string MaKhuyenMai { get; set; } = string.Empty;

        [StringLength(500)]
        [Display(Name = "Mô tả")]
        public string? MoTa { get; set; }

        [Required]
        [Display(Name = "Loại khuyến mãi")]
        public LoaiKhuyenMai LoaiKhuyenMai { get; set; }

        [Required]
        [Display(Name = "Giá trị")]
        [Range(0, double.MaxValue)]
        public decimal GiaTri { get; set; }

        [Display(Name = "Giá trị tối đa")]
        [Range(0, double.MaxValue)]
        public decimal? GiaTriToiDa { get; set; }

        [Display(Name = "Giá trị đơn hàng tối thiểu")]
        [Range(0, double.MaxValue)]
        public decimal? GiaTriDonHangToiThieu { get; set; }

        [Required]
        [Display(Name = "Ngày bắt đầu")]
        [DataType(DataType.DateTime)]
        public DateTime NgayBatDau { get; set; }

        [Required]
        [Display(Name = "Ngày kết thúc")]
        [DataType(DataType.DateTime)]
        public DateTime NgayKetThuc { get; set; }

        [Display(Name = "Số lượng")]
        public int? SoLuong { get; set; }

        [Display(Name = "Đã sử dụng")]
        public int DaSuDung { get; set; } = 0;

        [Display(Name = "Trạng thái hoạt động")]
        public bool TrangThaiHoatDong { get; set; } = true;

        [Display(Name = "Ngày tạo")]
        public DateTime NgayTao { get; set; } = DateTime.Now;
    }

    public enum LoaiKhuyenMai
    {
        [Display(Name = "Giảm theo phần trăm")]
        GiamPhanTram = 1,
        [Display(Name = "Giảm theo số tiền")]
        GiamSoTien = 2,
        [Display(Name = "Miễn phí")]
        MienPhi = 3
    }
}