<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Đặt Vé Xe</title>
    <link rel="icon" type="image/jpeg" href="~/images/realvscity.jpg" />
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/color-fixes.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/page-specific-fixes.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/DatVeXe.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark city-navbar mb-0">
            <div class="container-fluid">
                <a class="navbar-brand city-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-bus-front me-2"></i>DatVeXe
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-area="" asp-controller="Home" asp-action="Index">Trang chủ</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-area="" asp-controller="Booking" asp-action="Search">
                                <i class="bi bi-ticket-perforated me-1"></i>Đặt vé
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-area="" asp-controller="ChuyenXe" asp-action="Index">Chuyến xe</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-controller="Booking" asp-action="Search">
                                <i class="bi bi-ticket-perforated me-1"></i>Đặt vé
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-area="" asp-controller="ChuyenXe" asp-action="Search">
                                <i class="bi bi-search me-1"></i>Tìm chuyến xe
                            </a>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-controller="KhuyenMai" asp-action="Index">Khuyến mãi</a>
                        </li>
                    </ul>
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link city-nav-link" asp-controller="HoTro" asp-action="Index">
                                <i class="bi bi-question-circle me-1"></i> Hỗ trợ
                            </a>
                        </li>
                        @if (Context.Session.GetInt32("UserId") != null)
                        {
                            <li class="nav-item dropdown">
                                <a class="user-menu-btn nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle me-2"></i>
                                    <span class="fw-bold">@Context.Session.GetString("UserName")</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                    <li><a class="dropdown-item" href="/TaiKhoan/Dashboard"><i class="bi bi-speedometer2 me-2"></i>Dashboard</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" asp-controller="MyTickets" asp-action="Index"><i class="bi bi-ticket-perforated me-2"></i>Vé của tôi</a></li>
                                    <li><a class="dropdown-item" asp-controller="Contacts" asp-action="Index"><i class="bi bi-person-lines-fill me-2"></i>Danh bạ</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/TaiKhoan/Profile"><i class="bi bi-person-gear me-2"></i>Cập nhật thông tin</a></li>
                                    @if (Context.Session.GetInt32("IsAdmin") == 1)
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" asp-area="Admin" asp-controller="Admin" asp-action="Index"><i class="bi bi-gear me-2"></i>Admin Panel</a></li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="/TaiKhoan/DangXuat"><i class="bi bi-box-arrow-right me-2"></i>Đăng xuất</a></li>
                                </ul>
                            </li>
                        }
                        else
                        {
                            <li class="nav-item">
                                <a class="sign-in-btn nav-link" asp-controller="TaiKhoan" asp-action="Auth">
                                    <i class="bi bi-person-circle me-2"></i>
                                    <span class="fw-bold">Đăng nhập</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </div>
        </nav>
    </header>
    <div class="container">
        <main role="main" class="pb-3">
            <!-- Hiển thị cảnh báo bảo mật -->
            @if (TempData["ErrorType"] != null && TempData["ErrorType"].ToString() == "Unauthorized")
            {
                <div class="container mt-4">
                    <div class="alert alert-danger alert-dismissible fade show security-alert" role="alert">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-shield-alt fa-2x me-3 text-danger"></i>
                            <div>
                                <h5 class="alert-heading mb-1">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    @TempData["ErrorTitle"]
                                </h5>
                                <p class="mb-0">@TempData["ErrorMessage"]</p>
                                <hr class="my-2">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    Nếu bạn cần quyền truy cập, vui lòng liên hệ với quản trị viên hệ thống.
                                </small>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                </div>
            }
            @RenderBody()
        </main>
    </div>

    <footer class="border-top footer text-muted">
        <div class="container">
            &copy; 2025 - DatVeXe - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/color-contrast-checker.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
<style>
    .sign-in-btn, .user-menu-btn {
        display: inline-flex;
        align-items: center;
        background: #e3f0fc;
        border: 2px solid #b5d6f6;
        border-radius: 999px;
        padding: 4px 20px 4px 10px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: background 0.2s, border 0.2s;
        text-decoration: none;
        box-shadow: 0 2px 8px #e3f0fc;
        margin-left: 10px;
        color: #0d223f;
    }
    .sign-in-btn:hover, .user-menu-btn:hover {
        background: #d0e7fa;
        border-color: #7bb6ec;
        text-decoration: none;
        color: #0d223f;
    }
    .dropdown-menu {
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        padding: 0.5rem 0;
    }
    .dropdown-item {
        padding: 0.6rem 1.5rem;
        font-weight: 500;
        transition: all 0.2s;
    }
    .dropdown-item:hover {
        background-color: #e3f0fc;
    }
    .dropdown-item.text-danger:hover {
        background-color: #ffe5e5;
    }

    /* City Navbar Styles */
    .city-navbar {
        background: linear-gradient(135deg, #0a2640 0%, #1a3a5c 100%);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 0.75rem 0;
    }

    .city-brand {
        font-size: 1.5rem;
        font-weight: 700;
        color: #6cabdd !important;
        text-decoration: none;
        display: flex;
        align-items: center;
    }

    .city-brand:hover {
        color: #ffffff !important;
    }

    .city-nav-link {
        color: rgba(255, 255, 255, 0.9) !important;
        font-weight: 500;
        padding: 0.5rem 1rem !important;
        margin: 0 0.25rem;
        border-radius: 6px;
        transition: all 0.3s ease;
        position: relative;
    }

    .city-nav-link:hover {
        color: #6cabdd !important;
        background-color: rgba(108, 171, 221, 0.1);
        transform: translateY(-1px);
    }

    .city-nav-link.active {
        color: #6cabdd !important;
        background-color: rgba(108, 171, 221, 0.15);
    }

    .navbar-toggler {
        border-color: rgba(255, 255, 255, 0.3);
    }

    .navbar-toggler:focus {
        box-shadow: 0 0 0 0.2rem rgba(108, 171, 221, 0.25);
    }

    /* Security Alert Styles */
    .security-alert {
        border: 2px solid #dc3545;
        border-radius: 12px;
        background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        animation: securityPulse 2s ease-in-out;
    }

    .security-alert .alert-heading {
        color: #721c24;
        font-weight: 700;
    }

    .security-alert p {
        color: #721c24;
        font-weight: 500;
    }

    .security-alert hr {
        border-color: #dc3545;
        opacity: 0.3;
    }

    .security-alert .text-muted {
        color: #6c757d !important;
    }

    @@keyframes securityPulse {
        0% {
            transform: scale(0.95);
            opacity: 0;
        }
        50% {
            transform: scale(1.02);
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
</style>
</html>
