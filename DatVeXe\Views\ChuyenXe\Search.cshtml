@model DatVeXe.Models.ChuyenXeSearchViewModel
@{
    ViewData["Title"] = "Tìm kiếm nâng cao";
}

<div class="container py-4">
    <div class="d-flex flex-column flex-md-row justify-content-between align-items-center mb-4 gap-3">
        <h2 class="fw-bold text-primary mb-0 text-uppercase">Tìm kiếm chuyến xe nâng cao</h2>
        <div class="d-flex gap-2">
            <a asp-action="Index" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> Danh sách chuyến xe
            </a>
            <a asp-action="Dashboard" class="btn btn-info">
                <i class="fas fa-chart-bar"></i> Thống kê
            </a>
        </div>
    </div>

    <!-- Form tìm kiếm nâng cao -->
    <div class="card shadow-lg mb-4">
        <div class="card-header bg-gradient bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-search-plus"></i> Bộ lọc tìm kiếm
            </h5>
        </div>
        <div class="card-body bg-light">
            <form asp-action="Search" method="post" id="advancedSearchForm">
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h6 class="mb-0"><i class="fas fa-map-marker-alt text-primary"></i> Điểm đi/đến</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label asp-for="DiemDi" class="form-label fw-bold"></label>
                                    <select asp-for="DiemDi" class="form-select">
                                        <option value="">-- Chọn điểm đi --</option>
                                        @if (ViewBag.DiemDiList != null)
                                        {
                                            @foreach (var diem in (List<string>)ViewBag.DiemDiList)
                                            {
                                                <option value="@diem">@diem</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="DiemDen" class="form-label fw-bold"></label>
                                    <select asp-for="DiemDen" class="form-select">
                                        <option value="">-- Chọn điểm đến --</option>
                                        @if (ViewBag.DiemDenList != null)
                                        {
                                            @foreach (var diem in (List<string>)ViewBag.DiemDenList)
                                            {
                                                <option value="@diem">@diem</option>
                                            }
                                        }
                                    </select>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="swapDiemDiDen()">
                                    <i class="fas fa-exchange-alt"></i> Đổi chiều
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h6 class="mb-0"><i class="fas fa-calendar text-success"></i> Thời gian</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label asp-for="TuNgay" class="form-label fw-bold"></label>
                                    <input asp-for="TuNgay" class="form-control" type="date" />
                                </div>
                                <div class="mb-3">
                                    <label asp-for="DenNgay" class="form-label fw-bold"></label>
                                    <input asp-for="DenNgay" class="form-control" type="date" />
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="setToday()">
                                        Hôm nay
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="setThisWeek()">
                                        Tuần này
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-info" onclick="setThisMonth()">
                                        Tháng này
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-4 mt-2">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h6 class="mb-0"><i class="fas fa-dollar-sign text-success"></i> Lọc theo giá</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-6">
                                        <label asp-for="GiaTu" class="form-label fw-bold"></label>
                                        <input asp-for="GiaTu" class="form-control" placeholder="0" type="number" min="0" step="1000" />
                                        <span asp-validation-for="GiaTu" class="text-danger"></span>
                                    </div>
                                    <div class="col-6">
                                        <label asp-for="GiaDen" class="form-label fw-bold"></label>
                                        <input asp-for="GiaDen" class="form-control" placeholder="1000000" type="number" min="0" step="1000" />
                                        <span asp-validation-for="GiaDen" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <label asp-for="LoaiXe" class="form-label fw-bold"></label>
                                    <select asp-for="LoaiXe" class="form-select">
                                        <option value="">Tất cả loại xe</option>
                                        <option value="Giường nằm">Giường nằm</option>
                                        <option value="Limousine">Limousine</option>
                                        <option value="Ghế ngồi">Ghế ngồi</option>
                                        <option value="VIP">VIP</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h6 class="mb-0"><i class="fas fa-filter text-warning"></i> Bộ lọc khác</h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label asp-for="TrangThai" class="form-label fw-bold"></label>
                                    <select asp-for="TrangThai" class="form-select">
                                        <option value="">Tất cả trạng thái</option>
                                        <option value="chua_khoi_hanh">Chưa khởi hành</option>
                                        <option value="da_khoi_hanh">Đã khởi hành</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="SortBy" class="form-label fw-bold">Sắp xếp theo</label>
                                    <select asp-for="SortBy" class="form-select">
                                        <option value="NgayKhoiHanh">Ngày khởi hành</option>
                                        <option value="Gia">Giá vé</option>
                                        <option value="DiemDi">Điểm đi</option>
                                        <option value="DiemDen">Điểm đến</option>
                                        <option value="LoaiXe">Loại xe</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label asp-for="SortOrder" class="form-label fw-bold">Thứ tự</label>
                                    <select asp-for="SortOrder" class="form-select">
                                        <option value="asc">Tăng dần</option>
                                        <option value="desc">Giảm dần</option>
                                    </select>
                                </div>
                                <div class="form-check">
                                    <input name="CoGheTrong" class="form-check-input" type="checkbox" value="true" @(Model.CoGheTrong == true ? "checked" : "") />
                                    <label for="CoGheTrong" class="form-check-label fw-bold">Có ghế trống</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-white">
                                <h6 class="mb-0"><i class="fas fa-cogs text-danger"></i> Thao tác</h6>
                            </div>
                            <div class="card-body d-flex flex-column justify-content-center">
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="fas fa-search"></i> Tìm kiếm
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                                        <i class="fas fa-undo"></i> Đặt lại
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Kết quả tìm kiếm -->
    @if (Model.KetQua != null)
    {
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list-ul"></i> Kết quả tìm kiếm (@Model.KetQua.Count() chuyến xe)
                </h5>
            </div>
            <div class="card-body p-0">
                @if (Model.KetQua.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th>Điểm đi</th>
                                    <th>Điểm đến</th>
                                    <th>Ngày khởi hành</th>
                                    <th>Giá vé</th>
                                    <th>Xe</th>
                                    <th>Số ghế trống</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model.KetQua)
                                {
                                    var soGheTrong = (item.Xe?.SoGhe ?? 0) - (item.Ves?.Count ?? 0);
                                    var daDi = item.NgayKhoiHanh <= DateTime.Now;

                                    <tr>
                                        <td><strong>@item.DiemDiDisplay</strong></td>
                                        <td><strong>@item.DiemDenDisplay</strong></td>
                                        <td>@item.NgayKhoiHanh.ToString("dd/MM/yyyy HH:mm")</td>
                                        <td>
                                            <strong class="text-success">@item.Gia.ToString("N0") VNĐ</strong>
                                        </td>
                                        <td>
                                            @item.Xe.BienSo
                                            <small class="text-muted d-block">@item.Xe.LoaiXe - @item.Xe.SoGhe chỗ</small>
                                        </td>
                                        <td>
                                            @if (soGheTrong > 0)
                                            {
                                                <span class="badge bg-success">@soGheTrong chỗ trống</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Hết chỗ</span>
                                            }
                                        </td>
                                        <td>
                                            @if (daDi)
                                            {
                                                <span class="badge bg-secondary">Đã khởi hành</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-primary">Chưa khởi hành</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a asp-action="Details" asp-route-id="@item.ChuyenXeId"
                                                   class="btn btn-outline-primary btn-sm" title="Xem chi tiết">
                                                    <i class="fas fa-info-circle"></i>
                                                </a>
                                                @if (!daDi && soGheTrong > 0)
                                                {
                                                    <a asp-controller="ChoNgoi" asp-action="ChonGhe" asp-route-id="@item.ChuyenXeId"
                                                       class="btn btn-success btn-sm" title="Chọn chỗ ngồi">
                                                        <i class="bi bi-grid-3x3-gap"></i>
                                                    </a>
                                                }
                                                @if (Context.Session.GetInt32("IsAdmin") == 1 && !daDi)
                                                {
                                                    <a asp-action="Edit" asp-route-id="@item.ChuyenXeId"
                                                       class="btn btn-outline-warning btn-sm" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                }
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5 text-muted">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <h5>Không tìm thấy chuyến xe nào</h5>
                        <p>Vui lòng thử lại với điều kiện tìm kiếm khác</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@section Styles {
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .card {
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .bg-gradient {
            background-color: #007bff;
        }

        /* Fix text visibility issues */
        .form-label {
            color: #2c3e50 !important;
            font-weight: 600;
        }

        .form-check-label {
            color: #2c3e50 !important;
            font-weight: 500;
        }

        .card-header h6 {
            color: #2c3e50 !important;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .table th {
            color: #2c3e50 !important;
            font-weight: 600;
        }

        .table td {
            color: #495057 !important;
        }

        .table td strong {
            color: #2c3e50 !important;
        }

        .btn-outline-secondary {
            color: #6c757d;
            border-color: #6c757d;
        }

        .btn-outline-secondary:hover {
            color: #ffffff;
            background-color: #6c757d;
            border-color: #6c757d;
        }

        .btn-outline-info {
            color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-outline-info:hover {
            color: #ffffff;
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        /* Ensure all text in cards is visible */
        .card-body {
            color: #495057;
        }

        .card-body label,
        .card-body .form-label {
            color: #2c3e50 !important;
        }

        /* Fix any remaining text visibility issues */
        h2, h5, h6 {
            color: #2c3e50 !important;
        }

        .text-center h5 {
            color: #6c757d !important;
        }

        .text-center p {
            color: #6c757d !important;
        }
    </style>
}

@section Scripts {
    <script>
        function swapDiemDiDen() {
            var diemDi = $('#DiemDi').val();
            var diemDen = $('#DiemDen').val();
            $('#DiemDi').val(diemDen);
            $('#DiemDen').val(diemDi);
        }

        function setToday() {
            var today = new Date().toISOString().split('T')[0];
            $('#TuNgay').val(today);
            $('#DenNgay').val(today);
        }

        function setThisWeek() {
            var today = new Date();
            var firstDay = new Date(today.setDate(today.getDate() - today.getDay()));
            var lastDay = new Date(today.setDate(today.getDate() - today.getDay() + 6));

            $('#TuNgay').val(firstDay.toISOString().split('T')[0]);
            $('#DenNgay').val(lastDay.toISOString().split('T')[0]);
        }

        function setThisMonth() {
            var today = new Date();
            var firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            var lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

            $('#TuNgay').val(firstDay.toISOString().split('T')[0]);
            $('#DenNgay').val(lastDay.toISOString().split('T')[0]);
        }

        function resetForm() {
            $('#advancedSearchForm')[0].reset();
        }

        $(document).ready(function() {
            // Validation cho ngày
            $('#TuNgay, #DenNgay').change(function() {
                var tuNgay = $('#TuNgay').val();
                var denNgay = $('#DenNgay').val();

                if (tuNgay && denNgay && new Date(tuNgay) > new Date(denNgay)) {
                    alert('Từ ngày không được lớn hơn đến ngày');
                    $(this).val('');
                }
            });
        });
    </script>
}
