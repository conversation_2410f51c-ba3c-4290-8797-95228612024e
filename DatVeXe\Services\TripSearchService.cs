using DatVeXe.Models;
using Microsoft.EntityFrameworkCore;

namespace DatVeXe.Services
{
    public interface ITripSearchService
    {
        Task<List<ChuyenXe>> SearchTripsAsync(BookingSearchViewModel searchModel);
        Task<List<string>> GetDepartureLocationsAsync();
        Task<List<string>> GetDestinationLocationsAsync();
        Task<List<string>> GetBusCompaniesAsync();
        Task<List<string>> GetBusTypesAsync();
        int CalculateAvailableSeats(ChuyenXe chuyenXe);
        bool HasEnoughSeats(ChuyenXe chuyenXe, int passengerCount);
    }

    public class TripSearchService : ITripSearchService
    {
        private readonly DatVeXeContext _context;
        private readonly ILogger<TripSearchService> _logger;

        public TripSearchService(DatVeXeContext context, ILogger<TripSearchService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<ChuyenXe>> SearchTripsAsync(BookingSearchViewModel searchModel)
        {
            try
            {
                var query = _context.ChuyenXes
                    .Include(c => c.Xe)
                    .Include(c => c.TuyenDuong)
                    .Include(c => c.Ves.Where(v => v.TrangThai == TrangThaiVe.DaDat || 
                                                  v.TrangThai == TrangThaiVe.DaThanhToan ||
                                                  v.TrangThai == TrangThaiVe.DaSuDung))
                    .Where(c => c.TrangThaiChuyenXe == TrangThaiChuyenXe.HoatDong)
                    .AsQueryable();

                // Lọc theo điểm đi
                if (!string.IsNullOrEmpty(searchModel.DiemDi))
                {
                    query = query.Where(c => (c.TuyenDuong != null && c.TuyenDuong.DiemDi == searchModel.DiemDi) ||
                                           (c.TuyenDuong == null && c.DiemDi == searchModel.DiemDi));
                }

                // Lọc theo điểm đến
                if (!string.IsNullOrEmpty(searchModel.DiemDen))
                {
                    query = query.Where(c => (c.TuyenDuong != null && c.TuyenDuong.DiemDen == searchModel.DiemDen) ||
                                           (c.TuyenDuong == null && c.DiemDen == searchModel.DiemDen));
                }

                // Lọc theo ngày đi
                if (searchModel.NgayDi != default(DateTime))
                {
                    var startDate = searchModel.NgayDi.Date;
                    var endDate = startDate.AddDays(1);
                    query = query.Where(c => c.NgayKhoiHanh >= startDate && c.NgayKhoiHanh < endDate);
                }

                // Lọc theo giờ khởi hành
                if (searchModel.GioKhoiHanhTu.HasValue)
                {
                    query = query.Where(c => c.NgayKhoiHanh.TimeOfDay >= searchModel.GioKhoiHanhTu.Value);
                }

                if (searchModel.GioKhoiHanhDen.HasValue)
                {
                    query = query.Where(c => c.NgayKhoiHanh.TimeOfDay <= searchModel.GioKhoiHanhDen.Value);
                }

                // Lọc theo loại xe
                if (!string.IsNullOrEmpty(searchModel.LoaiXe))
                {
                    query = query.Where(c => c.Xe != null && c.Xe.LoaiXe == searchModel.LoaiXe);
                }

                // Lọc theo nhà xe
                if (!string.IsNullOrEmpty(searchModel.NhaXe))
                {
                    query = query.Where(c => c.Xe != null && c.Xe.NhaXe == searchModel.NhaXe);
                }

                // Lọc theo khoảng giá
                if (searchModel.GiaTu.HasValue)
                {
                    query = query.Where(c => c.Gia >= searchModel.GiaTu.Value);
                }

                if (searchModel.GiaDen.HasValue)
                {
                    query = query.Where(c => c.Gia <= searchModel.GiaDen.Value);
                }

                // Chỉ lấy chuyến xe chưa khởi hành
                query = query.Where(c => c.NgayKhoiHanh > DateTime.Now);

                // Áp dụng sắp xếp
                switch (searchModel.SortBy?.ToLower())
                {
                    case "gia":
                        query = searchModel.SortOrder == "desc" ?
                            query.OrderByDescending(c => c.Gia) :
                            query.OrderBy(c => c.Gia);
                        break;
                    case "nhaxe":
                        query = searchModel.SortOrder == "desc" ?
                            query.OrderByDescending(c => c.Xe!.NhaXe) :
                            query.OrderBy(c => c.Xe!.NhaXe);
                        break;
                    default: // NgayKhoiHanh
                        query = searchModel.SortOrder == "desc" ?
                            query.OrderByDescending(c => c.NgayKhoiHanh) :
                            query.OrderBy(c => c.NgayKhoiHanh);
                        break;
                }

                var chuyenXes = await query.ToListAsync();

                // Lọc chuyến xe có đủ ghế trống
                var result = chuyenXes.Where(c => HasEnoughSeats(c, searchModel.SoHanhKhach)).ToList();

                _logger.LogInformation($"Found {result.Count} trips matching search criteria");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching trips");
                return new List<ChuyenXe>();
            }
        }

        public async Task<List<string>> GetDepartureLocationsAsync()
        {
            try
            {
                var locations = await _context.TuyenDuongs
                    .Where(t => t.TrangThaiHoatDong)
                    .Select(t => t.DiemDi)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                // Thêm các điểm đi từ ChuyenXe không có TuyenDuong
                var additionalLocations = await _context.ChuyenXes
                    .Where(c => c.TuyenDuongId == null && !string.IsNullOrEmpty(c.DiemDi))
                    .Select(c => c.DiemDi!)
                    .Distinct()
                    .Where(d => !locations.Contains(d))
                    .ToListAsync();

                locations.AddRange(additionalLocations);
                return locations.OrderBy(l => l).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting departure locations");
                return new List<string>();
            }
        }

        public async Task<List<string>> GetDestinationLocationsAsync()
        {
            try
            {
                var locations = await _context.TuyenDuongs
                    .Where(t => t.TrangThaiHoatDong)
                    .Select(t => t.DiemDen)
                    .Distinct()
                    .OrderBy(d => d)
                    .ToListAsync();

                // Thêm các điểm đến từ ChuyenXe không có TuyenDuong
                var additionalLocations = await _context.ChuyenXes
                    .Where(c => c.TuyenDuongId == null && !string.IsNullOrEmpty(c.DiemDen))
                    .Select(c => c.DiemDen!)
                    .Distinct()
                    .Where(d => !locations.Contains(d))
                    .ToListAsync();

                locations.AddRange(additionalLocations);
                return locations.OrderBy(l => l).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting destination locations");
                return new List<string>();
            }
        }

        public async Task<List<string>> GetBusCompaniesAsync()
        {
            try
            {
                return await _context.Xes
                    .Where(x => x.TrangThaiHoatDong && !string.IsNullOrEmpty(x.NhaXe))
                    .Select(x => x.NhaXe!)
                    .Distinct()
                    .OrderBy(n => n)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting bus companies");
                return new List<string>();
            }
        }

        public async Task<List<string>> GetBusTypesAsync()
        {
            try
            {
                return await _context.Xes
                    .Where(x => x.TrangThaiHoatDong && !string.IsNullOrEmpty(x.LoaiXe))
                    .Select(x => x.LoaiXe!)
                    .Distinct()
                    .OrderBy(l => l)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting bus types");
                return new List<string>();
            }
        }

        public int CalculateAvailableSeats(ChuyenXe chuyenXe)
        {
            if (chuyenXe.Xe == null) return 0;

            var tongGhe = chuyenXe.Xe.SoGhe;
            var veDaDat = chuyenXe.Ves?.Count(v => v.TrangThai == TrangThaiVe.DaDat || 
                                                  v.TrangThai == TrangThaiVe.DaThanhToan ||
                                                  v.TrangThai == TrangThaiVe.DaSuDung) ?? 0;
            
            return Math.Max(0, tongGhe - veDaDat);
        }

        public bool HasEnoughSeats(ChuyenXe chuyenXe, int passengerCount)
        {
            return CalculateAvailableSeats(chuyenXe) >= passengerCount;
        }
    }
}
