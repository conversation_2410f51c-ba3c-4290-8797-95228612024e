@model IEnumerable<DatVeXe.Models.Xe>

@{
    ViewData["Title"] = "Quản lý xe";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3 style="color: #2c3e50; font-weight: 600;">
        <i class="fas fa-bus" style="color: #e74c3c;"></i>
        Quản lý xe
    </h3>
    <button class="btn" style="background-color: #e74c3c; border-color: #e74c3c; color: white;">
        <i class="fas fa-plus"></i>
        Thêm xe
    </button>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead style="background-color: #34495e; color: white;">
                    <tr>
                        <th>ID</th>
                        <th>Biển số xe</th>
                        <th>Loại xe</th>
                        <th>Số ghế</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var xe in Model)
                    {
                        <tr>
                            <td>@xe.XeId</td>
                            <td>@xe.BienSo</td>
                            <td>@xe.LoaiXe</td>
                            <td>@xe.SoGhe</td>
                            <td>
                                <span class="badge" style="background-color: #27ae60; color: white;">Hoạt động</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #3498db; color: #3498db;">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #f39c12; color: #f39c12;">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm" style="border: 1px solid #e74c3c; color: #e74c3c;">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="mt-3">
    <small class="text-muted">
        Tổng cộng: @Model.Count() xe
    </small>
</div>
