@model IEnumerable<DatVeXe.Models.NguoiDung>

@{
    ViewData["Title"] = "Quản lý người dùng";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-users"></i> @ViewData["Title"]
                    </h3>
                    <a asp-action="Create" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Thêm người dùng
                    </a>
                </div>
                <div class="card-body">
                    @if (TempData["Success"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle"></i> @TempData["Success"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }
                    @if (TempData["Error"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle"></i> @TempData["Error"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>
                                        <i class="fas fa-user"></i> @Html.DisplayNameFor(model => model.HoTen)
                                    </th>
                                    <th>
                                        <i class="fas fa-envelope"></i> Email
                                    </th>
                                    <th>
                                        <i class="fas fa-shield-alt"></i> Quyền
                                    </th>
                                    <th>
                                        <i class="fas fa-ticket-alt"></i> Số vé đã đặt
                                    </th>
                                    <th>
                                        <i class="fas fa-cogs"></i> Thao tác
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.Any())
                                {
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong class="text-primary">@Html.DisplayFor(modelItem => item.HoTen)</strong>
                                            </td>
                                            <td>
                                                <span class="text-muted">@Html.DisplayFor(modelItem => item.Email)</span>
                                            </td>
                                            <td>
                                                @if (item.LaAdmin)
                                                {
                                                    <span class="badge bg-danger">
                                                        <i class="fas fa-crown"></i> Admin
                                                    </span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-info">
                                                        <i class="fas fa-user"></i> Khách hàng
                                                    </span>
                                                }
                                            </td>
                                            <td>
                                                @{
                                                    var ticketCount = Context.RequestServices.GetService<DatVeXe.Models.DatVeXeContext>()
                                                        .Ves.Count(v => v.NguoiDungId == item.NguoiDungId);
                                                }
                                                <span class="badge bg-secondary">@ticketCount vé</span>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a asp-action="Details" asp-route-id="@item.NguoiDungId"
                                                       class="btn btn-outline-info btn-sm" title="Xem chi tiết">
                                                        <i class="fas fa-info-circle"></i>
                                                    </a>
                                                    <a asp-action="Edit" asp-route-id="@item.NguoiDungId"
                                                       class="btn btn-outline-warning btn-sm" title="Chỉnh sửa">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a asp-action="Delete" asp-route-id="@item.NguoiDungId"
                                                       class="btn btn-outline-danger btn-sm" title="Xóa">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="5" class="text-center text-muted py-4">
                                            <i class="fas fa-users fa-3x mb-3"></i>
                                            <p>Chưa có người dùng nào trong hệ thống</p>
                                            <a asp-action="Create" class="btn btn-primary">
                                                <i class="fas fa-plus"></i> Thêm người dùng đầu tiên
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    </script>
}
